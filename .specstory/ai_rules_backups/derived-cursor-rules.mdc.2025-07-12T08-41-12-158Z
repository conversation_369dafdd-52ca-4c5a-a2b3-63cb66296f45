
## HEADERS

## TECH STACK
- Next.js (App Router)
- TypeScript
- Tailwind CSS
- Radix UI
- shadcn/ui
- next-themes
- lucide-react

## PROJECT DOCUMENTATION & CONTEXT SYSTEM

## UI COMPONENTS & STYLING
- Use MCP Magic (21st.dev) to improve UI.
- Modern, eye-pleasing design with subtle gradients and blur effects.
- Consistent UI across the application, including the dashboard.
- Support for light and dark modes on all pages.
- Collapsible sidebar on desktop, automatically hidden on smaller screens.
- Smooth animations and transitions.
- Prioritize a clean and functional design.
- Apply glassmorphism effects with backdrop-blur and semi-transparent backgrounds.
- Use gradients for buttons, cards, and accents.
- Implement smooth animations and transitions.
- Ensure a consistent design system with a cohesive color palette.

## API ENDPOINTS
- `/api/shorten` – endpoint to shorten links.

## DATA TYPES & SCHEMAS
- ShortLink
- Alias
- User
- ClickAnalytics

## BACKEND INTEGRATION

## AUTHENTICATION & AUTHORIZATION

## WORKFLOW & RELEASE RULES

## DEBUGGING

## TESTING & VALIDATION

## SECURITY

## ERROR HANDLING & LOGGING

## CODE ORGANIZATION & STRUCTURE
- `app/` – pages and API routes
- `components/` – UI components
- `lib/` – types and utility functions
- `hooks/` – custom hooks
- `public/` – static assets
- `styles/` – global styles

## NAMING CONVENTIONS

## COMMENTS & DOCUMENTATION