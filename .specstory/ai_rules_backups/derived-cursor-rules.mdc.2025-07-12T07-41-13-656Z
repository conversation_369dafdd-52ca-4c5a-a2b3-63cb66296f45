
## HEADERS

## TECH STACK
- Next.js (App Router)
- TypeScript
- Tailwind CSS
- Radix UI

## PROJECT DOCUMENTATION & CONTEXT SYSTEM

## UI COMPONENTS & STYLING

## API ENDPOINTS

## DATA TYPES & SCHEMAS
- ShortLink
- Alias
- User
- ClickAnalytics

## BACKEND INTEGRATION

## AUTHENTICATION & AUTHORIZATION

## WORKFLOW & RELEASE RULES

## DEBUGGING

## TESTING & VALIDATION

## SECURITY

## ERROR HANDLING & LOGGING

## CODE ORGANIZATION & STRUCTURE
- `app/` – pages and API routes
- `components/` – UI components
- `lib/` – types and utility functions
- `hooks/` – custom hooks
- `public/` – static assets
- `styles/` – global styles

## NAMING CONVENTIONS

## COMMENTS & DOCUMENTATION