
## HEADERS

## TECH STACK
- Next.js (App Router)
- TypeScript
- Tailwind CSS
- Radix UI
- shadcn/ui
- next-themes
- lucide-react
- React Hook Form + Zod (for forms)
- Tailwind CSS animations (for animations)

## PROJECT DOCUMENTATION & CONTEXT SYSTEM
- To suppress CSS validation errors related to Tailwind CSS directives, add `/* stylelint-disable */` at the top of the `app/globals.css` file.
- Alternatively, configure VS Code/Cursor by creating a `.vscode/settings.json` file in the project root with the following content:
```json
{
  "css.validate": false,
  "scss.validate": false,
  "less.validate": false,
  "css.lint.unknownAtRules": "ignore",
  "scss.lint.unknownAtRules": "ignore",
  "tailwindCSS.includeLanguages": {
    "typescript": "typescript",
    "javascript": "javascript",
    "typescriptreact": "typescript",
    "javascriptreact": "javascript"
  },
  "files.associations": {
    "*.css": "tailwindcss"
  }
}
```

## UI COMPONENTS & STYLING
- Use MCP Magic (21st.dev) to improve UI.
- Modern, eye-pleasing design with subtle gradients and blur effects.
- Consistent UI across the application, including the dashboard.
- Support for light and dark modes on all pages.
- Collapsible sidebar on desktop, automatically hidden on smaller screens.
- Smooth animations and transitions.
- Prioritize a clean and functional design.
- Apply glassmorphism effects with backdrop-blur and semi-transparent backgrounds.
- Use gradients for buttons, cards, and accents.
- Implement smooth animations and transitions.
- Ensure a consistent design system with a cohesive color palette.
- Design should be stonned and professional, avoiding unnecessary flashy colors or childish elements.
- The service must look clean, neat, and professional.
- Ensure consistency in both light and dark modes.
- All UI elements must be in Polish.
- **Color Palette - Subdued and Professional:**
  - **Primary Colors:**
    ```css
    /* Light Mode */
    --background: hsl(0, 0%, 100%);
    --foreground: hsl(224, 71%, 4%);
    --card: hsl(0, 0%, 100%);
    --card-foreground: hsl(224, 71%, 4%);
    --popover: hsl(0, 0%, 100%);
    --popover-foreground: hsl(224, 71%, 4%);

    /* Primary - Subtle blue */
    --primary: hsl(221, 83%, 53%);
    --primary-foreground: hsl(210, 40%, 98%);

    /* Secondary - Neutral gray */
    --secondary: hsl(210, 40%, 96%);
    --secondary-foreground: hsl(222, 84%, 5%);

    /* Muted - Subdued background */
    --muted: hsl(210, 40%, 96%);
    --muted-foreground: hsl(215, 16%, 47%);

    /* Accent - Delicate accent */
    --accent: hsl(210, 40%, 96%);
    --accent-foreground: hsl(222, 84%, 5%);

    /* Dark Mode */
    --background: hsl(215 28% 17%);
    --foreground: hsl(213, 31%, 91%);
    --card: hsl(222 84% 5%);
    --card-foreground: hsl(213, 31%, 91%);

    --primary: hsl(217, 91%, 60%);
    --primary-foreground: hsl(222, 84%, 5%);

    --secondary: hsl(222, 47%, 11%);
    --secondary-foreground: hsl(213, 31%, 91%);

    --muted: hsl(223, 47%, 11%);
    --muted-foreground: hsl(215, 20%, 65%);
    ```
  - **Semantic Colors:**
    ```css
    /* Success - Subtle green */
    --success: hsl(142, 76%, 36%);
    --success-foreground: hsl(355, 7%, 97%);
    --success-muted: hsl(142, 76%, 95%);

    /* Warning - Subdued yellow */
    --warning: hsl(43, 96%, 56%);
    --warning-foreground: hsl(25, 95%, 5%);
    --warning-muted: hsl(43, 96%, 95%);

    /* Error - Professional red */
    --destructive: hsl(0, 84%, 60%);
    --destructive-foreground: hsl(210, 40%, 98%);
    --destructive-muted: hsl(0, 84%, 95%);
    ```
- **Layout Architecture:**
  - **Homepage:**
    - Layout: Two-column (25% sidebar, 75% main content)
    - Sidebar: Collapsed on mobile, sticky on desktop
    - Background: Subtle gradient with decorative elements
    - Cards: Glassmorphism with backdrop-blur-sm
  - **Dashboard:**
    - Layout: Sidebar + main content
    - Sidebar: Professional with hover effects, tooltips, search bar
    - Cards: Elevated with subtle shadows
    - Tables: Clean with alternating rows
- **UI Components - Guidelines:**
  - **Buttons:**
    ```typescript
    // Modern button styling with Tailwind CSS (Merged and updated based on new instructions)
    import { cva } from "class-variance-authority"

    const buttonVariants = cva(
    "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:scale-[1.02] active:scale-[0.98]",
    {
      variants: {
        variant: {
          default: "bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg hover:shadow-xl",
          destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-lg hover:shadow-xl",
          outline: "border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground shadow-md hover:shadow-lg hover:border-primary/50",
          secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-md hover:shadow-lg",
          ghost: "hover:bg-accent hover:text-accent-foreground hover:scale-105",
          link: "text-primary underline-offset-4 hover:underline hover:text-primary/80",
        },
        size: {
          sm: "h-9 px-3 text-xs rounded-md min-w-[44px]",
          default: "h-10 px-4 py-2 min-w-[44px]",
          lg: "h-11 px-8 rounded-md min-w-[44px]",
          xl: "h-12 px-6 text-base rounded-lg min-w-[44px]",
          icon: "h-10 w-10 min-w-[44px]",
        },
      },
      defaultVariants: {
        variant: "default",
        size: "default",
      }
    }
    )

    export { buttonVariants }
    ```
  - **Cards:**
    ```css
    /* Professional card styling */
    .card {
      @apply rounded-xl border bg-white/95 dark:bg-slate-800/90 text-card-foreground shadow-lg backdrop-blur-sm transition-all duration-200 ease-in-out hover:shadow-2xl hover:-translate-y-1 hover:bg-card/95;
    }

    /* Card header with gradient */
    .card-header {
      @apply flex flex-col space-y-1.5 p-6;
      background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
      border-radius: 0.75rem 0.75rem 0 0;
    }
    ```
  - **Input fields:**
    ```css
    .input {
      @apply flex h-10 w-full rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-2 text-sm text-slate-900 dark:text-slate-100;
      @apply ring-offset-background placeholder:text-muted-foreground;
      @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
      transition: all 0.2s ease-in-out;
    }

    .input:focus {
      border-color: hsl(var(--primary));
      box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
    }
    ```
- **Modernizations Specific to PVV.pl:**
  - **Header - Clean Navigation:**
    ```typescript
    // Clean header design
    - Minimal logo with icon
    - Subtle navigation
    - Theme toggle in the upper right corner
    - Mobile-first responsive design
    ```
  - **Sidebar - Professional Components:**

    In dark mode, the sidebar should have the same background as the middle blocks, or something similar, because the current dark background is too dark and looks unaesthetic.

    ```typescript
    // Sidebar structure
    ├── Login form (compact)
    ├── Quick URL shortener
    ├── Alias checker
    ├── Statistics widget
    └── Top aliases list

    // Styling guidelines
    - Compact card design
    - Hover effects on interactions
    - Loading states
    - Error states in subtle colors
    ```
  - **Dashboard - Enterprise Look:**
    ```typescript
    // Dashboard components
    ├── Stats cards (3-4 in one row)
    ├── Chart/analytics section  
    ├── Recent links table
    └── Quick actions panel

    // Professional table styling
    - Alternating row colors
    - Hover states
    - Action buttons in outline style
    - Pagination controls
    ```
  - **Forms - Improved Ergonomics:**
    ```typescript
    // Form improvements
    - Larger touch targets (min 44px)
    - Clear validation states
    - Subtle placeholders
    - Progress indicators
    - Auto-save indicators
    ```
- **Dark/Light Mode - Consistency:**
  - **Theme Switching:**
    ```typescript
    // Theme toggle component
    - Smooth transitions (0.3s ease-in-out)
    - System preference detection
    - Persistent user choice
    - Icon animation when switching
    ```
  - **Consistent Variables:**
    ```css
    /* Use CSS variables everywhere */
    color: hsl(var(--foreground));
    background-color: hsl(var(--background));
    border-color: hsl(var(--border));

    /* Avoid hardcoded colors */
    ❌ color: #3b82f6;
    ✅ color: hsl(var(--primary));
    ```
- **Animations and Transitions:**
  - **Subtle micro-interactions:**
    ```css
    /* Hover effects */
    .hover-lift {
      transition: all 0.2s ease-in-out;
    }
    .hover-lift:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.2);
    }

    /* Focus states */
    .focus-ring {
      @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2;
    }

    /* Loading states */
    .skeleton {
      @apply animate-pulse bg-muted rounded;
    }
    ```
- **Responsiveness:**
  - **Breakpoints:**
    ```css
    /* Mobile first approach */
    .responsive-grid {
      @apply grid grid-cols-1;
      @apply md:grid-cols-2; 
      @apply lg:grid-cols-3;
      @apply xl:grid-cols-4;
    }

    /* Sidebar behavior */
    .sidebar {
      @apply hidden md:block; /* Hidden on mobile */
      @apply md:w-64; /* Fixed width on desktop */
    }
    ```
- **Implementation Checklist:**
  - **Priority 1 - Basics:**
    - [x] Unifikacja komponentów przycisków (Completed)
    - [x] Poprawka stanów focus
    - [x] Responsive grid system
  - **Priority 2 - Components:**
    - [x] Redesign sidebar
    - [x] Modernize dashboard
    - [x] Improved forms
    - [x] Responsive tables
  - **Priority 3 - Details:**
    - [x] Animations and transitions
    - [x] Loading states
    - [x] Error handling UI
    - [x] Accessibility improvements
- **Usage Example:**
  ```typescript
  // New card appearance in the sidebar
  <Card className="bg-card/80 backdrop-blur-sm border-border shadow-lg hover:shadow-xl transition-all duration-300">
    <CardHeader className="bg-gradient-to-r from-primary/10 to-primary/5 border-b border-border/50">
      <CardTitle className="text-foreground flex items-center gap-2">
        <Icon className="h-5 w-5 text-primary" />
        Section Title
      </CardTitle>
    </CardHeader>
    <CardContent className="p-6">
      {/* Content */}
    </CardContent>
  </Card>
  ```
- **Modernization Rules:**
  - **Color Palette:**
    - **MUST** retain the current color scheme. Do not introduce new colors or modify existing color values.
  - **Localization:**
    - **ALL** text, links, and UI elements **MUST** be in Polish.
  - **Design Inspiration:**
    - The design should be inspired by professional layouts similar to dub.co/home.
  - **Interface Skeleton:**
    - Create a consistent and complete UI skeleton ready for logic implementation.
  - **Components:**
    - Follow the guidelines for buttons, cards and input fields to ensure consistency.
  - **Sidebar:**
    - Ensure the sidebar is collapsible on mobile and sticky on desktop.
  - **Responsiveness:**
    - Adopt a mobile-first approach to ensure optimal viewing experience across devices.
  - **Consistency:**
    - Maintain component consistency by using the same CSS classes throughout the project.
  - **Touch Targets:**
    - All interactive elements should have a minimum touch target size of 44px for improved accessibility.
  - **Sidebar Background:**
    - **MUST** ensure that in dark mode, the sidebar blocks (Logowanie do aliasów, Sprawdź dostępny alias, Podaj adres do skrócenia, TOP 5 - Najnowsze aliasy, TOP 5 - Najpopularniejsze, Statystyki na żywo) have the same background as the middle blocks, or something similar, because the current dark background is too dark and looks unaesthetic.
  - **Logo Usage:**
    - **MUST** use the logo from `public/logo.svg` everywhere in the website, including header and footer, instead of placeholders or icons.
    - **MUST** remove the text "PVV.pl" from both the header and footer and replace it with the logo.
    - **MUST** make the logo as large as the "PVV.pl" text was previously, so that it is easily readable.
    - **MUST** ensure the logo is displayed without any additional styling like borders or backgrounds, resembling plain text.
    - **MUST** increase the logo size by 50% to improve its readability and visual prominence.
  - **Logo Negativity:**
    - **SHOULD** use a negative version of the logo in light mode for both the header and footer.
    - Add `logo-invert` class to logo, and create the following css class:
        ```css
        .logo-invert {
          @apply transition-all duration-300 ease-in-out;
        }
        ```

## API ENDPOINTS
- `/api/shorten` – endpoint to shorten links.

## DATA TYPES & SCHEMAS
- ShortLink
- Alias
- User
- ClickAnalytics
```typescript
// Defined in lib/types.ts
interface ShortLink {
  id: string
  target_url: string
  user_id?: string
  type: "temporary" | "api"
  verified: boolean
  clicks: number
  expires_at: Date
  created_at: Date
}

interface Alias {
  id: string
  alias: string
  target_url: string
  user_id: string
  title?: string
  description?: string
  verified: boolean
  public: boolean
  clicks: number
  created_at: Date
  updated_at: Date
}

interface User {
  id: string
  email: string
  name?: string
  username?: string
  api_key: string
  created_at: Date
  email_verified?: Date
}

interface ClickAnalytics {
  id: number
  link_id: string
  link_type: "alias" | "short_link"
  timestamp: Date
  ip?: string
  country?: string
  user_agent?: string
  referer?: string
}
```

## BACKEND INTEGRATION

## AUTHENTICATION & AUTHORIZATION

## WORKFLOW & RELEASE RULES

## DEBUGGING
- To suppress CSS validation errors related to Tailwind CSS directives, add `/* stylelint-disable */` at the top of the `app/globals.css` file.
- Alternatively, configure VS Code/Cursor by creating a `.vscode/settings.json` file in the project root with the following content:
```json
{
  "css.validate": false,
  "scss.validate": false,
  "less.validate": false,
  "css.lint.unknownAtRules": "ignore",
  "scss.lint.unknownAtRules": "ignore",
  "tailwindCSS.includeLanguages": {
    "typescript": "typescript",
    "javascript": "javascript",
    "typescriptreact": "typescript",
    "javascriptreact": "javascript"
  },
  "files.associations": {
    "*.css": "tailwindcss"
  }
}
```

## TESTING & VALIDATION

## SECURITY

## ERROR HANDLING & LOGGING

## CODE ORGANIZATION & STRUCTURE
- `app/` – pages and API routes
- `components/` – UI components
- `lib/` – types and utility functions
- `hooks/` – custom hooks
- `public/` – static assets
- `styles/` – global styles

## NAMING CONVENTIONS

## COMMENTS & DOCUMENTATION
- To suppress CSS validation errors related to Tailwind CSS directives, add `/* stylelint-disable */` at the top of the `app/globals.css` file.
- Alternatively, configure VS Code/Cursor by creating a `.vscode/settings.json` file in the project root with the following content:
```json
{
  "css.validate": false,
  "scss.validate": false,
  "less.validate": false,
  "css.lint.unknownAtRules": "ignore",
  "scss.lint.unknownAtRules": "ignore",
  "tailwindCSS.includeLanguages": {
    "typescript": "typescript",
    "javascript": "javascript",
    "typescriptreact": "typescript",
    "javascriptreact": "javascript"
  },
  "files.associations": {
    "*.css": "tailwindcss"
  }
}
```
- **CSS Validation Errors**:
  - It's normal to see "Unknown at rule" errors for `@tailwind` and `@apply` in `globals.css`. These are due to the CSS Language Server not recognizing Tailwind directives.
  - To resolve this:
    1. Add `/* stylelint-disable */` at the top of `app/globals.css`.
    2. OR, configure VS Code/Cursor by creating `.vscode/settings.json` with the following content:
    ```json
    {
      "css.validate": false,
      "scss.validate": false,
      "less.validate": false,
      "css.lint.unknownAtRules": "ignore",
      "scss.lint.unknownAtRules": "ignore",
      "tailwindCSS.includeLanguages": {
        "typescript": "typescript",
        "javascript": "javascript",
        "typescriptreact": "typescript",
        "javascriptreact": "javascript"
      },
      "files.associations": {
        "*.css": "tailwindcss"
      }
    }
    ```
    3. OR, install the "Tailwind CSS IntelliSense" extension in VS Code/Cursor.

## CLOUDFLARED TUNNEL SETUP
- **Problem**: You might encounter a `Cross origin request detected` warning when using cloudflared tunnel.
- **Solution**:
  - Add `allowedDevOrigins: ['*']` to the `next.config.mjs` file for development mode. However, a better approach is to use the following configuration.

- **Example `next.config.mjs`:**
```javascript
  experimental: {
    allowedRevalidateHeaderKeys: ['*'],
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ]
  },
  ...(process.env.NODE_ENV === 'development' && {
    async rewrites() {
      return {
        beforeFiles: [],
        afterFiles: [],
        fallback: [],
      }
    },
  }),
```

- **`cloudflared-config.yml` Example:**
```yaml
tunnel: your-tunnel-name
credentials-file: /path/to/your/credentials-file.json

ingress:
  - hostname: your-domain.trycloudflare.com
    service: http://localhost:3000
  - service: http_status:404

# Alternately, for a quick tunnel (without a domain):
# Use: cloudflared tunnel --url http://localhost:3000
```

- **`CLOUDFLARED_SETUP.md` Example:**
```md
# Cloudflared Tunnel Configuration for PVV.pl

## Quick Start (Quick Tunnel)

1. Ensure the development server is running:
   ```bash
   npm run dev
   ```

2. In a second terminal, run cloudflared tunnel:
   ```bash
   cloudflared tunnel --url http://localhost:3000
   ```

3. Cloudflared will generate a public URL to share.

## Persistent Configuration (Named Tunnel)

1. Install cloudflared.
2. Log in to Cloudflare:
   ```bash
   cloudflared tunnel login
   ```

3. Create a tunnel:
   ```bash
   cloudflared tunnel create pvv-dev
   ```

4. Edit `cloudflared-config.yml` with your data.
5. Run the tunnel:
   ```bash
   cloudflared tunnel --config cloudflared-config.yml run
   ```

## Troubleshooting

✅ **Fixed**: Cross-origin requests are now handled.
- The application is configured for tunnels.
- All assets will function correctly.
- No CORS warnings appear in development mode.

## Testing

Verify resources load correctly:
- Homepage: `your-tunnel-url.trycloudflare.com`
- Dashboard: `your-tunnel-url.trycloudflare.com/dashboard`
- API: `your-tunnel-url.trycloudflare.com/api/shorten`
```