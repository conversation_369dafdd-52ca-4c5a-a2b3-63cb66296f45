import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card"
import { BarChart3, <PERSON>, <PERSON>, <PERSON>Pointer, Globe, Wifi } from "lucide-react"

export function Statistics() {
  const stats = [
    { label: "aliasów w serwisie", value: "282", icon: Link, color: "text-cyan-500 dark:text-cyan-400" },
    {
      label: "unikalnych wizyt do aliasów",
      value: "1,660,106",
      icon: Mouse<PERSON>ointer,
      color: "text-emerald-500 dark:text-emerald-400",
    },
    { label: "linków w serwisie", value: "24,802", icon: Globe, color: "text-violet-500 dark:text-violet-400" },
    {
      label: "skróconych kliknięć w linki",
      value: "923,198",
      icon: BarChart3,
      color: "text-orange-500 dark:text-orange-400",
    },
    { label: "osób na stronie", value: "6", icon: Users, color: "text-pink-500 dark:text-pink-400" },
  ]

  return (
    <Card className="bg-white/95 dark:bg-slate-800/90 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 shadow-xl">
      <CardHeader className="bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-800 text-slate-800 dark:text-white rounded-t-xl">
        <CardTitle className="text-lg flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          Statystyki na żywo
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-3">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon
            return (
              <div
                key={index}
                className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors border border-transparent hover:border-border"
              >
                <div className={`p-2 rounded-lg bg-muted shadow-sm ${stat.color}`}>
                  <IconComponent className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <div className="font-bold text-lg text-foreground">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </div>
              </div>
            )
          })}
        </div>

        <div className="mt-6 p-4 bg-gradient-to-r from-muted/50 to-cyan-50/50 dark:from-muted dark:to-muted/80 rounded-lg border-l-4 border-cyan-500 dark:border-cyan-400 border border-border/50">
          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
            <Wifi className="h-4 w-4 text-cyan-500 dark:text-cyan-400" />
            <span className="font-medium">Twój adres IP:</span>
          </div>
          <div className="bg-background p-3 rounded border border-border overflow-hidden">
            <p className="font-mono text-xs text-foreground break-all">
              2a02:2454:8577:d300:fc39:b469:878b:ad88
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
