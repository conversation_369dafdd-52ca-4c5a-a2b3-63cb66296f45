"use client"

import React, { useState, useId } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input, Label } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { User, Lock, LogIn, UserPlus, Key } from "lucide-react"

export function LoginForm() {
  const [email, setEmail] = useState("")
  const emailInputId = useId();
  const passwordInputId = useId();
  const [password, setPassword] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Login attempt:", { email, password })
  }

  return (
    <Card className="bg-white/95 dark:bg-slate-800/90 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 shadow-xl">
      <CardHeader className="bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-800 text-slate-800 dark:text-white rounded-t-xl">
        <CardTitle className="text-lg flex items-center gap-2">
          <User className="h-5 w-5" />
          Logowanie do aliasów
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 p-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center gap-2">
 <User className="h-4 w-4" />
              Login:
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Wprowadź email"
              required
            />
          </div>
          <div className="space-y-2">
 <Label htmlFor={passwordInputId} className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              Hasło:
            </Label>
            <Input
 id={passwordInputId}
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Wprowadź hasło"
              required
            />
          </div>
          <div className="flex gap-2">
            <Button type="submit" className="flex-1" size="lg">
              <LogIn className="h-4 w-4 mr-2" />
              Zaloguj się
            </Button>
            <Button variant="outline" className="flex-1" size="lg">
              <UserPlus className="h-4 w-4 mr-2" />
              Rejestracja
            </Button>
          </div>
        </form>
        <div className="text-center pt-2">
          <Button variant="link" size="sm">
            <Key className="h-4 w-4 mr-1" />
            Przypomnienie hasła
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
