"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input, Label } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>2, <PERSON><PERSON><PERSON>, Zap } from "lucide-react"

export function LinkShortener() {
  const [url, setUrl] = useState("https://")

  const handleShorten = () => {
    console.log("Shortening URL:", url)
  }

  return (
    <Card className="bg-white/95 dark:bg-slate-800/90 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 shadow-xl">
      <CardHeader className="bg-gradient-to-r from-violet-100 to-purple-200 dark:from-violet-600 dark:to-purple-700 text-violet-800 dark:text-white rounded-t-xl">
        <CardTitle className="text-lg flex items-center gap-2">
          <Scissors className="h-5 w-5" />
          S<PERSON><PERSON><PERSON><PERSON> szy<PERSON><PERSON> długi link
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 p-6">
        <div className="space-y-2">
          <Label htmlFor="url" className="flex items-center gap-2">
            <Link2 className="h-4 w-4" />
            Podaj adres do skrócenia:
          </Label>
          <Input
            id="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="https://example.com"
          />
        </div>
        <Button onClick={handleShorten} className="w-full" size="lg">
          <Zap className="h-4 w-4 mr-2" />
          Skróć link natychmiast
        </Button>
      </CardContent>
    </Card>
  )
}
