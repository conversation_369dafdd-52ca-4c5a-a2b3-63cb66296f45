"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Search, Globe, CheckCircle } from "lucide-react"

export function AliasChecker() {
  const [alias, setAlias] = useState("")

  const handleCheck = () => {
    console.log("Checking alias:", alias)
  }

  return (
    <Card className="bg-white/95 dark:bg-slate-800/90 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 shadow-xl">
      <CardHeader className="bg-gradient-to-r from-emerald-100 to-teal-200 dark:from-emerald-600 dark:to-teal-700 text-emerald-800 dark:text-white rounded-t-xl">
        <CardTitle className="text-lg flex items-center gap-2">
          <Search className="h-5 w-5" />
          Sprawd<PERSON> dostępny alias
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 p-6">
        <div className="space-y-3">
          <div className="flex items-center gap-1 text-sm text-muted-foreground p-3 rounded-lg bg-muted/50 border border-border/50">
            <Globe className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
            <span className="font-mono">_____</span>
            <span className="font-semibold text-emerald-600 dark:text-emerald-400">.pvv.pl</span>
          </div>
          <Input
            value={alias}
            onChange={(e) => setAlias(e.target.value)}
            placeholder="Wprowadź alias"
            className="text-center font-mono"
          />
        </div>
        <Button onClick={handleCheck} className="w-full" size="lg">
          <CheckCircle className="h-4 w-4 mr-2" />
          Sprawdź dostępność
        </Button>
      </CardContent>
    </Card>
  )
}
