import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import Link from "next/link"
import { TrendingUp, Star, ExternalLink, Clock } from "lucide-react"

export function TopAliases() {
  const topAliases = [
    { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", domain: "pvv.pl", clicks: "1.2k" },
    { name: "str<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", domain: "pvv.pl", clicks: "856" },
    { name: "gm2024", domain: "pvv.pl", clicks: "743" },
    { name: "festyna", domain: "pvv.pl", clicks: "621" },
    { name: "tdi", domain: "pvv.pl", clicks: "498" },
  ]

  const topPopular = [
    { name: "sp3lubartow", domain: "pvv.pl", clicks: "5.2k" },
    { name: "zsokyna", domain: "pvv.pl", clicks: "4.1k" },
    { name: "t-motywator", domain: "pvv.pl", clicks: "3.8k" },
    { name: "sbooki", domain: "pvv.pl", clicks: "2.9k" },
    { name: "lop", domain: "pvv.pl", clicks: "2.1k" },
  ]

  return (
    <div className="space-y-6">
      <Card className="bg-white/95 dark:bg-slate-800/90 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 shadow-xl">
        <CardHeader className="bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-800 text-slate-800 dark:text-white rounded-t-xl">
          <CardTitle className="text-lg flex items-center gap-2">
            <Clock className="h-5 w-5" />
            TOP 5 - Najnowsze aliasy
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <ul className="space-y-2">
            {topAliases.map((alias, index) => (
              <li key={index} className="group">
                <div className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-all duration-200 border border-transparent hover:border-border">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-slate-600 to-slate-700 dark:from-slate-700 dark:to-slate-800 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-sm">
                      {index + 1}
                    </div>
                    <Link
                      href={`https://${alias.name}.${alias.domain}`}
                      className="text-primary hover:text-primary/80 font-medium transition-colors group-hover:underline"
                    >
                      {alias.name}.{alias.domain}
                    </Link>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <span>{alias.clicks}</span>
                    <ExternalLink className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      <Card className="bg-white/95 dark:bg-slate-800/90 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 shadow-xl">
        <CardHeader className="bg-gradient-to-r from-emerald-100 to-teal-200 dark:from-emerald-600 dark:to-teal-700 text-emerald-800 dark:text-white rounded-t-xl">
          <CardTitle className="text-lg flex items-center gap-2">
            <Star className="h-5 w-5" />
            TOP 5 - Najpopularniejsze
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <ul className="space-y-2">
            {topPopular.map((alias, index) => (
              <li key={index} className="group">
                <div className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-all duration-200 border border-transparent hover:border-border">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 dark:from-emerald-600 dark:to-teal-700 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-sm">
                      {index + 1}
                    </div>
                    <Link
                      href={`https://${alias.name}.${alias.domain}`}
                      className="text-primary hover:text-primary/80 font-medium transition-colors group-hover:underline"
                    >
                      {alias.name}.{alias.domain}
                    </Link>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <TrendingUp className="h-4 w-4 text-emerald-500 dark:text-emerald-400" />
                    <span className="font-semibold">{alias.clicks}</span>
                    <ExternalLink className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
