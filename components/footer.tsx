import { Heart, Code } from "lucide-react"
import Image from "next/image"

export function Footer() {
  return (
    <footer className="w-full bg-gradient-to-br from-slate-50 via-blue-50/30 to-cyan-50/30 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 py-8 mt-12 border-t border-border/30">
      <div className="container mx-auto px-4">
        <div className="bg-gradient-to-r from-slate-100 via-slate-50 to-slate-100 dark:from-slate-800 dark:via-slate-700 dark:to-slate-800 text-slate-700 dark:text-white rounded-xl px-6 py-8 border border-border/30 hover-lift shadow-lg">
          <div className="flex flex-col items-center text-center space-y-6">
            {/* Logo */}
            <div className="flex justify-center">
              <Image
                src="/logo.svg"
                alt="PVV.pl logo"
                width={168}
                height={168}
                className="object-contain invert dark:invert-0 logo-invert"
              />
            </div>
            
            {/* Created with love */}
            <div className="flex items-center justify-center gap-2 text-slate-600 dark:text-slate-400">
              <span>Stworzone z</span>
              <Heart className="h-4 w-4 text-pink-500 dark:text-pink-300 animate-pulse" />
              <span>i</span>
              <Code className="h-4 w-4 text-cyan-500 dark:text-cyan-300 hover-scale" />
              <span>dla społeczności</span>
            </div>
            
            {/* Copyright */}
            <div className="space-y-2">
              <p className="text-sm text-slate-600 dark:text-slate-400">
                Copyright © 2009-2025{" "}
                <span className="text-cyan-600 dark:text-cyan-200 font-semibold hover:text-cyan-500 dark:hover:text-cyan-100 transition-colors">
                  pvv.pl
                </span>{" "}
                - Wszelkie prawa zastrzeżone.
              </p>
              
              {/* Features tagline */}
              <div className="pt-4 border-t border-slate-300/50 dark:border-slate-600/30">
                <div className="flex flex-wrap justify-center gap-2 text-xs text-slate-500 dark:text-slate-500">
                  <span className="px-2 py-1 bg-slate-200/60 dark:bg-slate-700/30 rounded-full hover:bg-slate-200/80 dark:hover:bg-slate-600/50 transition-colors">
                    Nowa wersja
                  </span>
                  <span className="px-2 py-1 bg-slate-200/60 dark:bg-slate-700/30 rounded-full hover:bg-slate-200/80 dark:hover:bg-slate-600/50 transition-colors">
                    Szybsza
                  </span>
                  <span className="px-2 py-1 bg-slate-200/60 dark:bg-slate-700/30 rounded-full hover:bg-slate-200/80 dark:hover:bg-slate-600/50 transition-colors">
                    Bezpieczniejsza
                  </span>
                  <span className="px-2 py-1 bg-slate-200/60 dark:bg-slate-700/30 rounded-full hover:bg-slate-200/80 dark:hover:bg-slate-600/50 transition-colors">
                    Bardziej nowoczesna
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
