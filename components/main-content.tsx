import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input, Label } from "@/components/ui/input"
import { Globe, Zap, Shield, Sparkles, ArrowRight } from "lucide-react"

export function MainContent() {
  return (
    <div className="space-y-8">
      {/* Link Shortening Section - Dub.co inspired */}
      <Card className="bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 shadow-2xl">
        <CardContent className="pt-12 pb-12">
          <div className="text-center mb-12">
            <h1 className="text-5xl md:text-6xl font-bold text-slate-900 dark:text-slate-100 mb-6 leading-tight">
              Skracaj linki
              <br />
              <span className="bg-gradient-to-r from-cyan-500 to-blue-600 dark:from-cyan-400 dark:to-blue-500 bg-clip-text text-transparent">
                b<PERSON><PERSON><PERSON><PERSON><PERSON>
              </span>
            </h1>
            <p className="text-xl text-slate-600 dark:text-slate-400 max-w-2xl mx-auto leading-relaxed">
              Profesjonalne skracanie linków z zaawansowaną analityką. Twórz krótkie, zapamiętywalne linki w sekundach.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="bg-white dark:bg-slate-900 p-2 rounded-2xl border-2 border-slate-200 dark:border-slate-700 shadow-xl hover:shadow-2xl transition-all duration-300">
              <div className="flex flex-col md:flex-row gap-2">
                <div className="flex-1">
                  <Input
                    defaultValue="https://"
                    className="border-0 focus:ring-0 text-lg p-6 bg-transparent text-slate-900 dark:text-slate-100 placeholder:text-slate-400 dark:placeholder:text-slate-500"
                    placeholder="Wklej długi link tutaj..."
                  />
                </div>
                <Button className="bg-gradient-to-r from-cyan-500 to-blue-600 dark:from-cyan-400 dark:to-blue-500 hover:from-cyan-600 hover:to-blue-700 dark:hover:from-cyan-500 dark:hover:to-blue-600 text-white px-8 py-6 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200" aria-label="Skróć link">
                  <Zap className="h-5 w-5 mr-2" />
                  Skróć link
                </Button>
              </div>
            </div>

            <div className="mt-8 text-center">
              <p className="text-sm text-slate-500 dark:text-slate-400 mb-4">
                Opcjonalnie możesz dostosować swój link:
              </p>
              <div className="flex flex-col md:flex-row gap-4 max-w-2xl mx-auto">
                <div className="flex-1">
                  <Label className="text-sm text-slate-600 dark:text-slate-400 mb-2 block">Niestandardowa nazwa</Label>
                  <Input
                    className="border-2 border-slate-200 dark:border-slate-600 focus:border-cyan-400 dark:focus:border-cyan-300 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100"
                    placeholder="moj-link (opcjonalne)"
                  />
                </div>
                <div className="flex-1">
                  <Label className="text-sm text-slate-600 dark:text-slate-400 mb-2 block">Wygaśnięcie</Label>
                  <select id="expiry" className="w-full border-2 border-slate-200 dark:border-slate-600 focus:border-cyan-400 dark:focus:border-cyan-300 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100 p-3">
                    <option>24 godziny</option>
                    <option>7 dni</option>
                    <option>30 dni</option>
                    <option>Bez wygaśnięcia</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-6 rounded-xl bg-slate-50 dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700">
                <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-600 dark:from-cyan-400 dark:to-blue-500 rounded-lg mx-auto mb-4 flex items-center justify-center">
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <h2 className="font-semibold text-slate-900 dark:text-slate-100 mb-2">Błyskawiczne</h2>
                <p className="text-sm text-slate-600 dark:text-slate-400">Skracaj linki w ułamku sekundy</p>
              </div>
              <div className="text-center p-6 rounded-xl bg-slate-50 dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700">
                <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 dark:from-emerald-400 dark:to-teal-500 rounded-lg mx-auto mb-4 flex items-center justify-center">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <h2 className="font-semibold text-slate-900 dark:text-slate-100 mb-2">Bezpieczne</h2>
                <p className="text-sm text-slate-600 dark:text-slate-400">Weryfikacja każdego linku</p>
              </div>
              <div className="text-center p-6 rounded-xl bg-slate-50 dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700">
                <div className="w-12 h-12 bg-gradient-to-br from-violet-500 to-purple-600 dark:from-violet-400 dark:to-purple-500 rounded-lg mx-auto mb-4 flex items-center justify-center">
                  <Sparkles className="h-6 w-6 text-white" />
                </div>
                <h2 className="font-semibold text-slate-900 dark:text-slate-100 mb-2">Zaawansowane</h2>
                <p className="text-sm text-slate-600 dark:text-slate-400">Pełna analityka i statystyki</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Aliases Section */}
      <Card className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 shadow-xl">
        <CardContent className="pt-8 pb-8">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="p-4 bg-gradient-to-br from-cyan-500 to-blue-600 dark:from-cyan-400 dark:to-blue-500 rounded-full shadow-lg">
                <Globe className="h-8 w-8 text-white" />
              </div>
            </div>
            <h2 className="text-3xl font-bold text-slate-800 dark:text-slate-100 mb-4">Aliasy www</h2>
          </div>

          <div className="max-w-4xl mx-auto space-y-6">
            <p className="text-slate-700 dark:text-slate-300 text-lg leading-relaxed text-center">
              Nie musisz być posiadaczem długiego i nieatrakcyjnego adresu internetowego www, z którego jesteś
              niezadowolony - nasz system darmowego maskowania adresów www, czyli aliasów www umożliwia posiadanie
              krótkiej, unikalnej i atrakcyjnej nazwy w postaci:
            </p>

            <div className="text-center">
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-blue-600 dark:from-cyan-400 dark:to-blue-500 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                <Globe className="h-6 w-6" />
                <span className="text-xl font-bold">ALIAS.pvv.pl</span>
                <ArrowRight className="h-5 w-5" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-cyan-50 to-blue-50/50 dark:from-cyan-900/20 dark:to-blue-900/20 p-6 rounded-2xl border border-cyan-200/50 dark:border-cyan-700/50">
              <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
                Każdy założony alias www można opisać za pomocą znaczników META, dzięki temu gwarantuje to poprawną
                indeksację w wyszukiwarkach oraz zdobycie wysokiej pozycji i popularności. Aliasy są tworzone zgodnie z
                zasadami DNS.
              </p>
            </div>

            <div className="text-center bg-gradient-to-r from-emerald-50 to-teal-50/50 dark:from-emerald-900/20 dark:to-teal-900/20 p-8 rounded-2xl border border-emerald-200/50 dark:border-emerald-700/50">
              <h2 className="text-2xl font-bold text-emerald-800 dark:text-emerald-200 mb-3">
                Zarejestruj swój alias - zanim zrobi to ktoś inny...
              </h2>
              <p className="text-emerald-700 dark:text-emerald-300 text-lg">
                Pamiętaj, prostota i atrakcyjność adresu strony www jest jednym z kluczy do sukcesu.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Service Info */}
      <Card className="bg-gradient-to-r from-emerald-50 to-teal-50/50 dark:from-emerald-900/20 dark:to-teal-900/20 border border-emerald-200/50 dark:border-emerald-700/50 shadow-xl">
        <CardContent className="pt-6">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-emerald-500 dark:bg-emerald-600 rounded-full shadow-lg">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-emerald-800 dark:text-emerald-200 mb-2">
                Bezpieczeństwo i niezawodność
              </h3>
              <p className="text-emerald-700 dark:text-emerald-300 leading-relaxed">
                Serwis pvv.pl zlokalizowany jest na stabilnej platformie, która gwarantuje szybkość, niezawodność oraz
                stabilność prowadzonych usług. Pamiętaj, nie udostępniamy miejsca na twoja stronę internetową, lecz
                oferujemy Ci alias lub link w domenie pvv.pl ukrywający prawdziwą lokalizację Twojej strony bądź linku.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cookie Information */}
      <Card className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 shadow-xl">
        <CardContent className="pt-6">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-slate-500 dark:bg-slate-600 rounded-full shadow-lg">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-2">Informacja o Cookies</h3>
              <p className="text-slate-700 dark:text-slate-300">
                Korzystanie z serwisu pvv.pl - darmowe aliasy www i krótkie linki, oznacza zgodę na wykorzystywanie
                plików cookie. Więcej informacji można znaleźć w naszej polityce plików cookies.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
