/* stylelint-disable */
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* Hover Effects */
  .hover-lift {
    @apply transition-all duration-200 ease-in-out;
  }
  .hover-lift:hover {
    @apply -translate-y-0.5 shadow-xl;
  }
  
  .hover-scale {
    @apply transition-transform duration-200 ease-in-out;
  }
  .hover-scale:hover {
    @apply scale-105;
  }
  
  .hover-glow {
    @apply transition-all duration-200 ease-in-out;
  }
  .hover-glow:hover {
    @apply shadow-2xl shadow-primary/25;
  }
  
  /* Focus States */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 transition-all duration-200;
  }
  
  /* Loading States */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
  }
  
  .loading-dots {
    @apply animate-pulse;
  }
  
  .spinner {
    @apply animate-spin;
  }
  
  /* Custom Animations - bez @apply */
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  .slide-in-from-top {
    animation: slideInFromTop 0.3s ease-in-out;
  }
  
  /* Glassmorphism */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20 shadow-xl;
  }
  
  .glass-strong {
    @apply bg-white/20 backdrop-blur-lg border border-white/30 shadow-2xl;
  }
  
  .glass-subtle {
    @apply bg-white/5 backdrop-blur-sm border border-white/10 shadow-lg;
  }
  
  /* Responsive Grid */
  .responsive-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }
  
  /* Smooth Scrolling */
  .smooth-scroll {
    @apply scroll-smooth;
  }
  
  /* Custom Scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    @apply w-2;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-muted;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-primary/50;
  }
  
  /* Logo Inversion Transition */
  .logo-invert {
    @apply transition-all duration-300 ease-in-out;
  }
}

/* Custom keyframes for animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 215 25% 27%;
    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;
    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;
    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 215 25% 27%;
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;
    --accent: 210 40% 96%;
    --accent-foreground: 215 25% 27%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 221 83% 53%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 215 28% 17%;
    --foreground: 210 40% 98%;
    --card: 222 84% 5%;
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 5%;
    --popover-foreground: 210 40% 98%;
    --primary: 217 91% 60%;
    --primary-foreground: 222 84% 5%;
    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 215 28% 17%;
    --muted-foreground: 218 11% 65%;
    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 224 71% 4%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground smooth-scroll;
  }
}
