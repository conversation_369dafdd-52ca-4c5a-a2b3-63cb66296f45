import { Suspense } from "react"
import { Head<PERSON> } from "@/components/header"
import { Sidebar } from "@/components/sidebar"
import { MainContent } from "@/components/main-content"
import { Footer } from "@/components/footer"
import { Loader2 } from "lucide-react"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-cyan-50/30 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <Header />
      <div className="container mx-auto px-4 py-6 lg:py-8">
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
          {/* Sidebar - Hidden on mobile, sticky on desktop */}
          <aside className="lg:w-1/4 lg:sticky lg:top-24 lg:self-start order-2 lg:order-1">
            <div className="hidden lg:block">
              <Suspense
                fallback={
                  <div className="space-y-4">
                    <div className="flex items-center justify-center h-32 bg-card rounded-xl border border-border">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    </div>
                    <div className="skeleton h-24 rounded-xl"></div>
                    <div className="skeleton h-32 rounded-xl"></div>
                  </div>
                }
              >
                <Sidebar />
              </Suspense>
            </div>
            
            {/* Mobile Sidebar Toggle */}
            <div className="lg:hidden mb-6">
              <details className="group">
                <summary className="flex cursor-pointer items-center justify-between rounded-xl bg-card p-4 text-foreground shadow-lg hover:shadow-xl transition-all duration-200 border border-border">
                  <h2 className="text-lg font-semibold">Narzędzia</h2>
                  <svg className="h-5 w-5 transition-transform group-open:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </summary>
                <div className="mt-4 slide-in-from-top">
                  <Suspense
                    fallback={
                      <div className="space-y-4">
                        <div className="skeleton h-32 rounded-xl"></div>
                        <div className="skeleton h-24 rounded-xl"></div>
                      </div>
                    }
                  >
                    <Sidebar />
                  </Suspense>
                </div>
              </details>
            </div>
          </aside>

          {/* Main Content */}
          <main className="flex-1 order-1 lg:order-2">
            <div className="fade-in">
              <MainContent />
            </div>
          </main>
        </div>
        
        {/* Mobile Bottom Navigation Helper */}
        <div className="lg:hidden mt-8 p-4 bg-card rounded-xl border border-border text-center">
          <p className="text-sm text-muted-foreground mb-2">
            Potrzebujesz więcej funkcji?
          </p>
          <p className="text-xs text-muted-foreground">
            Przejdź na większy ekran aby uzyskać dostęp do pełnego sidebara z narzędziami
          </p>
        </div>
      </div>
      <Footer />
    </div>
  )
}
