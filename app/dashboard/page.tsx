import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { User, BarChart3, Zap, Key, RefreshCcw, ExternalLink, Eye, Calendar, Plus, Code, LineChart } from "lucide-react"

export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-cyan-50/30 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <Header />
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
          <p className="text-muted-foreground mt-1">Zarządzaj swoimi linkami i aliasami</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* User Profile */}
          <Card>
            <CardHeader className="bg-gradient-to-r from-cyan-100 to-blue-200 dark:from-cyan-600 dark:to-blue-700 text-cyan-800 dark:text-white rounded-t-xl">
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Profil użytkownika
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground">Email:</p>
                  <p className="font-medium text-foreground"><EMAIL></p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <Key className="h-3.5 w-3.5" />
                    API Key:
                  </p>
                  <div className="relative">
                    <p className="font-mono text-xs bg-muted p-2 rounded text-foreground border border-border">
                      pk_live_123456789abcdef
                    </p>
                  </div>
                </div>
                <Button variant="outline" size="sm" className="w-full">
                  <RefreshCcw className="h-4 w-4 mr-2" />
                  Regeneruj API Key
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          <Card>
            <CardHeader className="bg-gradient-to-r from-emerald-100 to-teal-200 dark:from-emerald-600 dark:to-teal-700 text-emerald-800 dark:text-white rounded-t-xl">
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Twoje statystyki
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Aliasy:</span>
                  <span className="font-medium text-xl text-foreground">5</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Skrócone linki:</span>
                  <span className="font-medium text-xl text-foreground">23</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Łączne kliknięcia:</span>
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-xl text-foreground">1,247</span>
                    <div className="px-2 py-1 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400 text-xs rounded-full">
                      +12%
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader className="bg-gradient-to-r from-violet-100 to-purple-200 dark:from-violet-600 dark:to-purple-700 text-violet-800 dark:text-white rounded-t-xl">
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Szybkie akcje
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 p-6">
              <Button className="w-full" size="lg">
                <Plus className="h-4 w-4 mr-2" />
                Utwórz nowy alias
              </Button>
              <Button variant="outline" className="w-full" size="lg">
                <Code className="h-4 w-4 mr-2" />
                Skróć link przez API
              </Button>
              <Button variant="outline" className="w-full" size="lg">
                <LineChart className="h-4 w-4 mr-2" />
                Zobacz statystyki
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Recent Links */}
        <Card>
          <CardHeader className="border-b border-border">
            <CardTitle className="text-xl flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Ostatnie linki
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-border bg-muted/30">
                    <th className="text-left p-4 text-muted-foreground font-medium">ID</th>
                    <th className="text-left p-4 text-muted-foreground font-medium">Docelowy URL</th>
                    <th className="text-left p-4 text-muted-foreground font-medium">Typ</th>
                    <th className="text-left p-4 text-muted-foreground font-medium">Kliknięcia</th>
                    <th className="text-left p-4 text-muted-foreground font-medium">Wygasa</th>
                    <th className="text-left p-4 text-muted-foreground font-medium">Akcje</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-border hover:bg-muted/30 transition-colors">
                    <td className="p-4">
                      <div className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-cyan-500 mr-2"></div>
                        <span className="font-mono text-foreground">Ab3kX9</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center">
                        <span className="text-foreground truncate max-w-[200px]">https://example.com</span>
                        <ExternalLink className="h-3.5 w-3.5 ml-2 text-muted-foreground" />
                      </div>
                    </td>
                    <td className="p-4">
                      <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-xs">
                        API
                      </span>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center text-foreground">
                        <Eye className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
                        42
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center text-foreground">
                        <Calendar className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
                        2025-12-31
                      </div>
                    </td>
                    <td className="p-4">
                      <Button variant="outline" size="sm">
                        Edytuj
                      </Button>
                    </td>
                  </tr>
                  <tr className="border-b border-border hover:bg-muted/30 transition-colors">
                    <td className="p-4">
                      <div className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-emerald-500 mr-2"></div>
                        <span className="font-mono text-foreground">3kX9mP</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center">
                        <span className="text-foreground truncate max-w-[200px]">https://another.com</span>
                        <ExternalLink className="h-3.5 w-3.5 ml-2 text-muted-foreground" />
                      </div>
                    </td>
                    <td className="p-4">
                      <span className="px-2 py-1 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400 rounded-full text-xs">
                        Tymczasowy
                      </span>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center text-foreground">
                        <Eye className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
                        15
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center text-foreground">
                        <Calendar className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
                        2025-01-13
                      </div>
                    </td>
                    <td className="p-4">
                      <Button variant="outline" size="sm">
                        Statystyki
                      </Button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div className="p-4 border-t border-border flex justify-between items-center bg-muted/20">
              <span className="text-sm text-muted-foreground">Wyświetlanie 2 z 25 linków</span>
              <Button variant="outline" size="sm">
                Zobacz wszystkie
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
      <Footer />
    </div>
  )
}
