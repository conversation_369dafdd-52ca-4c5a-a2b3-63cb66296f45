import { type NextRequest, NextResponse } from "next/server"

// Temporary ID generation (starts with digit)
function generateTempId(): string {
  const digits = "0123456789"
  const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

  const firstChar = digits[Math.floor(Math.random() * digits.length)]
  let result = firstChar

  for (let i = 0; i < 5; i++) {
    result += chars[Math.floor(Math.random() * chars.length)]
  }

  return result
}

// API ID generation (starts with letter)
function generateApiId(): string {
  const letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
  const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

  const firstChar = letters[Math.floor(Math.random() * letters.length)]
  let result = firstChar

  for (let i = 0; i < 5; i++) {
    result += chars[Math.floor(Math.random() * chars.length)]
  }

  return result
}

export async function POST(request: NextRequest) {
  try {
    const { url, type = "temporary" } = await request.json()

    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 })
    }

    // Verify URL security (placeholder - integrate with your verification API)
    const isSecure = await verifyUrlSecurity(url)
    if (!isSecure) {
      return NextResponse.json({ error: "URL failed security verification" }, { status: 400 })
    }

    // Generate appropriate ID based on type
    const linkId = type === "temporary" ? generateTempId() : generateApiId()

    // Calculate expiration
    const expiresAt = new Date()
    if (type === "temporary") {
      expiresAt.setHours(expiresAt.getHours() + 24) // 24 hours
    } else {
      expiresAt.setDate(expiresAt.getDate() + 365) // 365 days
    }

    // Store in database (placeholder)
    // await storeShortLink({ id: linkId, url, type, expiresAt })

    return NextResponse.json({
      id: linkId,
      shortUrl: `https://pvv.pl/${linkId}`,
      originalUrl: url,
      type,
      expiresAt: expiresAt.toISOString(),
      verified: true,
    })
  } catch (error) {
    console.error("Error shortening URL:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

async function verifyUrlSecurity(url: string): Promise<boolean> {
  // Placeholder for URL verification
  // This should integrate with your existing verification API
  try {
    // Basic URL validation
    new URL(url)
    return true
  } catch {
    return false
  }
}
